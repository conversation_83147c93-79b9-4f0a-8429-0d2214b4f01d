package cn.jihong.equipment.app.mapper;

import cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备字段数据记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface DeviceFieldDataRecordMapper extends BaseMapper<DeviceFieldDataRecordPO> {

    /**
     * 根据设备ID和时间范围查询字段数据记录
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 字段数据记录列表
     */
    @Select("<script>" +
            "SELECT * FROM device_field_data_records " +
            "WHERE device_id = #{deviceId} AND deleted = 0 " +
            "<if test='startTime != null'> AND collection_timestamp >= #{startTime} </if>" +
            "<if test='endTime != null'> AND collection_timestamp &lt;= #{endTime} </if>" +
            "ORDER BY collection_timestamp DESC, field_code ASC" +
            "</script>")
    List<DeviceFieldDataRecordPO> selectByDeviceIdAndTimeRange(
            @Param("deviceId") Integer deviceId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    /**
     * 根据批次ID查询字段数据记录
     *
     * @param batchId 批次ID
     * @return 字段数据记录列表
     */
    @Select("SELECT * FROM device_field_data_records " +
            "WHERE collection_batch_id = #{batchId} AND deleted = 0 " +
            "ORDER BY field_code ASC")
    List<DeviceFieldDataRecordPO> selectByBatchId(@Param("batchId") String batchId);

    /**
     * 根据设备ID和字段编码查询最新的字段数据记录
     *
     * @param deviceId  设备ID
     * @param fieldCode 字段编码
     * @return 最新的字段数据记录
     */
    @Select("SELECT * FROM device_field_data_records " +
            "WHERE device_id = #{deviceId} AND field_code = #{fieldCode} AND deleted = 0 " +
            "ORDER BY collection_timestamp DESC LIMIT 1")
    DeviceFieldDataRecordPO selectLatestByDeviceIdAndFieldCode(
            @Param("deviceId") Integer deviceId,
            @Param("fieldCode") String fieldCode);

    /**
     * 根据设备ID查询最新的所有字段数据记录
     *
     * @param deviceId 设备ID
     * @return 最新的字段数据记录列表
     */
    @Select("SELECT d.* FROM device_field_data_records d " +
            "INNER JOIN (" +
            "    SELECT device_id, field_code, MAX(collection_timestamp) as max_time " +
            "    FROM device_field_data_records " +
            "    WHERE device_id = #{deviceId} AND deleted = 0 " +
            "    GROUP BY device_id, field_code" +
            ") latest ON d.device_id = latest.device_id " +
            "    AND d.field_code = latest.field_code " +
            "    AND d.collection_timestamp = latest.max_time " +
            "WHERE d.deleted = 0 " +
            "ORDER BY d.field_code ASC")
    List<DeviceFieldDataRecordPO> selectLatestByDeviceId(@Param("deviceId") Integer deviceId);

    /**
     * 根据公司编码查询字段数据记录
     *
     * @param companyCode 公司编码
     * @param limit       限制条数
     * @return 字段数据记录列表
     */
    @Select("SELECT * FROM device_field_data_records " +
            "WHERE company_code = #{companyCode} AND deleted = 0 " +
            "ORDER BY collection_timestamp DESC " +
            "LIMIT #{limit}")
    List<DeviceFieldDataRecordPO> selectByCompanyCode(
            @Param("companyCode") String companyCode,
            @Param("limit") Integer limit);

    /**
     * 统计设备字段数据记录数量
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    @Select("<script>" +
            "SELECT " +
            "    COUNT(*) as total_count, " +
            "    COUNT(DISTINCT field_code) as field_count, " +
            "    COUNT(DISTINCT collection_batch_id) as batch_count, " +
            "    SUM(CASE WHEN quality = 'GOOD' THEN 1 ELSE 0 END) as good_count, " +
            "    SUM(CASE WHEN quality = 'BAD' THEN 1 ELSE 0 END) as bad_count, " +
            "    SUM(CASE WHEN quality = 'UNCERTAIN' THEN 1 ELSE 0 END) as uncertain_count " +
            "FROM device_field_data_records " +
            "WHERE device_id = #{deviceId} AND deleted = 0 " +
            "<if test='startTime != null'> AND collection_timestamp >= #{startTime} </if>" +
            "<if test='endTime != null'> AND collection_timestamp &lt;= #{endTime} </if>" +
            "</script>")
    Map<String, Object> selectStatsByDeviceIdAndTimeRange(
            @Param("deviceId") Integer deviceId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    /**
     * 批量插入字段数据记录
     *
     * @param records 字段数据记录列表
     * @return 插入的记录数
     */
    int insertBatch(@Param("records") List<DeviceFieldDataRecordPO> records);

    /**
     * 根据字段ID查询相关的数据记录数量
     *
     * @param fieldId 字段ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM device_field_data_records " +
            "WHERE field_id = #{fieldId} AND deleted = 0")
    Long countByFieldId(@Param("fieldId") Integer fieldId);

    /**
     * 根据寄存器ID查询相关的数据记录数量
     *
     * @param registerId 寄存器ID
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM device_field_data_records " +
            "WHERE register_id = #{registerId} AND deleted = 0")
    Long countByRegisterId(@Param("registerId") Integer registerId);

    /**
     * 根据设备ID和字段编码列表查询历史字段数据记录
     *
     * @param deviceId   设备ID
     * @param fieldCodes 字段编码列表
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param limit      限制条数
     * @return 字段数据记录列表
     */
    @Select("<script>" +
            "SELECT * FROM device_field_data_records " +
            "WHERE device_id = #{deviceId} AND deleted = 0 " +
            "<if test='fieldCodes != null and fieldCodes.size() > 0'>" +
            "  AND field_code IN " +
            "  <foreach collection='fieldCodes' item='fieldCode' open='(' separator=',' close=')'>" +
            "    #{fieldCode}" +
            "  </foreach>" +
            "</if>" +
            "<if test='startTime != null'> AND collection_timestamp >= #{startTime} </if>" +
            "<if test='endTime != null'> AND collection_timestamp &lt;= #{endTime} </if>" +
            "ORDER BY collection_timestamp DESC, field_code ASC " +
            "<if test='limit != null'> LIMIT #{limit} </if>" +
            "</script>")
    List<DeviceFieldDataRecordPO> selectByDeviceIdAndFieldCodes(
            @Param("deviceId") Integer deviceId,
            @Param("fieldCodes") List<String> fieldCodes,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("limit") Integer limit);

    /**
     * 根据设备ID和字段编码列表查询最新的字段数据记录
     *
     * @param deviceId   设备ID
     * @param fieldCodes 字段编码列表
     * @return 最新的字段数据记录列表
     */
    @Select("<script>" +
            "SELECT d.* FROM device_field_data_records d " +
            "INNER JOIN (" +
            "    SELECT device_id, field_code, MAX(collection_timestamp) as max_time " +
            "    FROM device_field_data_records " +
            "    WHERE device_id = #{deviceId} AND deleted = 0 " +
            "    <if test='fieldCodes != null and fieldCodes.size() > 0'>" +
            "      AND field_code IN " +
            "      <foreach collection='fieldCodes' item='fieldCode' open='(' separator=',' close=')'>" +
            "        #{fieldCode}" +
            "      </foreach>" +
            "    </if>" +
            "    GROUP BY device_id, field_code" +
            ") latest ON d.device_id = latest.device_id " +
            "    AND d.field_code = latest.field_code " +
            "    AND d.collection_timestamp = latest.max_time " +
            "WHERE d.deleted = 0 " +
            "ORDER BY d.field_code ASC" +
            "</script>")
    List<DeviceFieldDataRecordPO> selectLatestByDeviceIdAndFieldCodes(
            @Param("deviceId") Integer deviceId,
            @Param("fieldCodes") List<String> fieldCodes);
}
