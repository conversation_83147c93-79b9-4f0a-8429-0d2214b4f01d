package cn.jihong.equipment.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO;
import cn.jihong.equipment.api.model.vo.out.DeviceFieldDataBatchVO;
import cn.jihong.equipment.api.service.DataProcessingService;
import cn.jihong.equipment.api.service.IDeviceDataService;
import cn.jihong.equipment.api.service.IDeviceFieldDataRecordService;
import cn.jihong.equipment.app.protocol.factory.DeviceProtocolFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备数据控制器
 * 提供设备数据查询功能，支持新的字段数据记录查询和原有的历史数据查询
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/device-data")
@ShenyuSpringMvcClient(path = "/device-data/**")
public class DeviceDataController {

    @Autowired
    private IDeviceDataService deviceDataService;

    @Autowired
    private IDeviceFieldDataRecordService deviceFieldDataRecordService;

    @Autowired
    private DataProcessingService dataProcessingService;

    @Autowired
    private DeviceProtocolFactory protocolFactory;

    /**
     * 查询设备字段数据记录（按批次分组）
     */
    @GetMapping("/field-records/{deviceId}")
    public StandardResult<List<DeviceFieldDataBatchVO>> getDeviceFieldDataRecords(
            @PathVariable Integer deviceId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getDeviceFieldDataRecordsByBatch(deviceId, startTime, endTime));
    }

    /**
     * 查询设备字段数据记录（原始格式，每个字段一条记录）
     */
    @GetMapping("/field-records/raw/{deviceId}")
    public StandardResult<List<DeviceFieldDataRecordPO>> getDeviceFieldDataRecordsRaw(
            @PathVariable Integer deviceId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getDeviceFieldDataRecords(deviceId, startTime, endTime));
    }

    /**
     * 获取设备最新字段数据记录
     */
    @GetMapping("/field-records/latest/{deviceId}")
    public StandardResult<List<DeviceFieldDataRecordPO>> getLatestDeviceFieldData(@PathVariable Integer deviceId) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getLatestDeviceFieldData(deviceId));
    }

    /**
     * 根据批次ID查询字段数据记录
     */
    @GetMapping("/field-records/batch/{batchId}")
    public StandardResult<List<DeviceFieldDataRecordPO>> getFieldDataByBatchId(@PathVariable String batchId) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getFieldDataByBatchId(batchId));
    }

    /**
     * 根据公司编码查询字段数据记录
     */
    @GetMapping("/field-records/company/{companyCode}")
    public StandardResult<List<DeviceFieldDataRecordPO>> getCompanyFieldDataRecords(
            @PathVariable String companyCode,
            @RequestParam(defaultValue = "1000") Integer limit) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getCompanyFieldDataRecords(companyCode, limit));
    }

    /**
     * 获取设备字段数据统计信息
     */
    @GetMapping("/field-records/stats/{deviceId}")
    public StandardResult<Map<String, Object>> getDeviceFieldDataStats(
            @PathVariable Integer deviceId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getDeviceFieldDataStats(deviceId, startTime, endTime));
    }

    /**
     * 获取设备最新字段数据（Map格式，兼容原有接口）
     */
    @GetMapping("/field-records/latest-map/{deviceId}")
    public StandardResult<Map<String, Object>> getLatestDeviceFieldDataAsMap(@PathVariable Integer deviceId) {
        List<DeviceFieldDataRecordPO> records = deviceDataService.getLatestDeviceFieldData(deviceId);
        Map<String, Object> dataMap = deviceDataService.convertFieldRecordsToMap(records);
        return StandardResult.resultCode(OperateCode.SUCCESS, dataMap);
    }

    /**
     * 根据设备ID和字段编码获取最新字段值
     */
    @GetMapping("/field-records/latest/{deviceId}/{fieldCode}")
    public StandardResult<DeviceFieldDataRecordPO> getLatestFieldValue(
            @PathVariable Integer deviceId,
            @PathVariable String fieldCode) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getLatestFieldValue(deviceId, fieldCode));
    }

    /**
     * 根据设备ID和字段编码列表获取历史字段值
     * 支持动态表头，前端传入需要查询的字段，后台返回对应的采集数据用于表格展示
     * 支持日期过滤，且日期范围不超过一周
     * 如果设备ID为空，则返回所有设备的字段信息
     *
     * @param deviceId   设备ID（可选，为空时查询所有设备）
     * @param fieldCodes 字段编码列表
     * @param startTime  开始时间（可选）
     * @param endTime    结束时间（可选）
     * @param limit      限制条数（可选，默认1000）
     * @return 历史字段数据记录和表头信息
     */
    @PostMapping("/field-records/history")
    public StandardResult<Map<String, Object>> getHistoryFieldData(
            @RequestParam(required = false) Integer deviceId,
            @RequestBody List<String> fieldCodes,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(required = false, defaultValue = "1000") Integer limit) {

        try {
            log.info("查询历史字段数据 - 设备ID: {}, 字段数: {}, 时间范围: {} - {}, 限制条数: {}", 
                    deviceId, fieldCodes != null ? fieldCodes.size() : 0, startTime, endTime, limit);

            // 验证参数
            if (CollectionUtils.isEmpty(fieldCodes)) {
                return StandardResult.resultCode(OperateCode.FAIL, "字段编码列表不能为空");
            }

            // 验证日期范围不超过一周
            if (startTime != null && endTime != null) {
                long diffInMillis = endTime.getTime() - startTime.getTime();
                long diffInDays = diffInMillis / (24 * 60 * 60 * 1000);
                if (diffInDays > 7) {
                    return StandardResult.resultCode(OperateCode.FAIL, "查询时间范围不能超过一周");
                }
                if (diffInMillis < 0) {
                    return StandardResult.resultCode(OperateCode.FAIL, "开始时间不能晚于结束时间");
                }
            }

            Map<String, Object> result = new HashMap<>();
            List<DeviceFieldDataRecordPO> records;

            if (deviceId != null) {
                // 查询指定设备的字段数据
                records = deviceDataService.getDeviceFieldDataRecordsByFieldCodes(
                        deviceId, fieldCodes, startTime, endTime, limit);
            } else {
                // 查询所有设备的字段数据（按公司编码限制）
                records = getAllDevicesFieldData(fieldCodes, startTime, endTime, limit);
            }

            // 构建表头信息
            List<Map<String, Object>> headers = buildTableHeaders(fieldCodes, records);
            
            // 构建表格数据
            List<Map<String, Object>> tableData = buildTableData(records, fieldCodes);

            result.put("headers", headers);
            result.put("data", tableData);
            result.put("total", records.size());
            result.put("fieldCodes", fieldCodes);
            result.put("deviceId", deviceId);

            log.info("查询完成 - 返回记录数: {}, 表头数: {}", records.size(), headers.size());
            return StandardResult.resultCode(OperateCode.SUCCESS, result);

        } catch (Exception e) {
            log.error("查询历史字段数据失败", e);
            return StandardResult.resultCode(OperateCode.FAIL, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据设备ID和字段编码列表获取最新字段值
     * 支持动态表头，返回每个字段的最新值
     *
     * @param deviceId   设备ID（可选，为空时查询所有设备）
     * @param fieldCodes 字段编码列表
     * @return 最新字段数据记录和表头信息
     */
    @PostMapping("/field-records/latest-by-codes")
    public StandardResult<Map<String, Object>> getLatestFieldDataByCodes(
            @RequestParam(required = false) Integer deviceId,
            @RequestBody List<String> fieldCodes) {

        try {
            log.info("查询最新字段数据 - 设备ID: {}, 字段数: {}", 
                    deviceId, fieldCodes != null ? fieldCodes.size() : 0);

            // 验证参数
            if (CollectionUtils.isEmpty(fieldCodes)) {
                return StandardResult.resultCode(OperateCode.FAIL, "字段编码列表不能为空");
            }

            Map<String, Object> result = new HashMap<>();
            List<DeviceFieldDataRecordPO> records;

            if (deviceId != null) {
                // 查询指定设备的最新字段数据
                records = deviceDataService.getLatestDeviceFieldDataByFieldCodes(deviceId, fieldCodes);
            } else {
                // 查询所有设备的最新字段数据
                records = getAllDevicesLatestFieldData(fieldCodes);
            }

            // 构建表头信息
            List<Map<String, Object>> headers = buildTableHeaders(fieldCodes, records);
            
            // 构建表格数据
            List<Map<String, Object>> tableData = buildTableData(records, fieldCodes);

            result.put("headers", headers);
            result.put("data", tableData);
            result.put("total", records.size());
            result.put("fieldCodes", fieldCodes);
            result.put("deviceId", deviceId);

            log.info("查询完成 - 返回记录数: {}, 表头数: {}", records.size(), headers.size());
            return StandardResult.resultCode(OperateCode.SUCCESS, result);

        } catch (Exception e) {
            log.error("查询最新字段数据失败", e);
            return StandardResult.resultCode(OperateCode.FAIL, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有设备的字段数据（当设备ID为空时使用）
     */
    private List<DeviceFieldDataRecordPO> getAllDevicesFieldData(List<String> fieldCodes, 
                                                                Date startTime, Date endTime, Integer limit) {
        // 这里需要根据具体业务需求实现
        // 可以通过公司编码或其他条件来限制查询范围
        // 暂时返回空列表，避免查询过多数据
        log.warn("查询所有设备字段数据功能暂未实现，返回空结果");
        return new ArrayList<>();
    }

    /**
     * 获取所有设备的最新字段数据
     */
    private List<DeviceFieldDataRecordPO> getAllDevicesLatestFieldData(List<String> fieldCodes) {
        // 这里需要根据具体业务需求实现
        // 可以通过公司编码或其他条件来限制查询范围
        log.warn("查询所有设备最新字段数据功能暂未实现，返回空结果");
        return new ArrayList<>();
    }

    /**
     * 构建表头信息
     */
    private List<Map<String, Object>> buildTableHeaders(List<String> fieldCodes, 
                                                       List<DeviceFieldDataRecordPO> records) {
        List<Map<String, Object>> headers = new ArrayList<>();

        // 添加基础列
        headers.add(createHeader("deviceId", "设备ID", "number"));
        headers.add(createHeader("collectionTimestamp", "采集时间", "datetime"));

        // 添加字段列
        for (String fieldCode : fieldCodes) {
            // 从记录中查找字段信息
            DeviceFieldDataRecordPO sampleRecord = records.stream()
                    .filter(r -> fieldCode.equals(r.getFieldCode()))
                    .findFirst()
                    .orElse(null);

            String fieldName = sampleRecord != null ? sampleRecord.getFieldName() : fieldCode;
            String dataType = sampleRecord != null ? sampleRecord.getDataType() : "string";
            String unit = sampleRecord != null ? sampleRecord.getUnit() : "";

            Map<String, Object> header = createHeader(fieldCode, fieldName, dataType);
            if (unit != null && !unit.trim().isEmpty()) {
                header.put("unit", unit);
            }
            headers.add(header);
        }

        return headers;
    }

    /**
     * 创建表头对象
     */
    private Map<String, Object> createHeader(String key, String title, String dataType) {
        Map<String, Object> header = new HashMap<>();
        header.put("key", key);
        header.put("title", title);
        header.put("dataType", dataType);
        return header;
    }

    /**
     * 构建表格数据
     */
    private List<Map<String, Object>> buildTableData(List<DeviceFieldDataRecordPO> records, 
                                                    List<String> fieldCodes) {
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }

        // 按设备ID和采集时间分组
        Map<String, List<DeviceFieldDataRecordPO>> groupedRecords = records.stream()
                .collect(Collectors.groupingBy(r -> r.getDeviceId() + "_" + 
                        (r.getCollectionTimestamp() != null ? r.getCollectionTimestamp().getTime() : 0)));

        List<Map<String, Object>> tableData = new ArrayList<>();

        for (List<DeviceFieldDataRecordPO> group : groupedRecords.values()) {
            if (CollectionUtils.isEmpty(group)) {
                continue;
            }

            DeviceFieldDataRecordPO firstRecord = group.get(0);
            Map<String, Object> row = new HashMap<>();

            // 设置基础信息
            row.put("deviceId", firstRecord.getDeviceId());
            row.put("collectionTimestamp", firstRecord.getCollectionTimestamp());
            row.put("collectionBatchId", firstRecord.getCollectionBatchId());

            // 设置字段值
            Map<String, DeviceFieldDataRecordPO> fieldMap = group.stream()
                    .collect(Collectors.toMap(
                            DeviceFieldDataRecordPO::getFieldCode,
                            r -> r,
                            (existing, replacement) -> existing
                    ));

            for (String fieldCode : fieldCodes) {
                DeviceFieldDataRecordPO fieldRecord = fieldMap.get(fieldCode);
                if (fieldRecord != null) {
                    Object value = convertFieldValue(fieldRecord.getFieldValue(), fieldRecord.getDataType());
                    row.put(fieldCode, value);
                    row.put(fieldCode + "_quality", fieldRecord.getQuality());
                } else {
                    row.put(fieldCode, null);
                    row.put(fieldCode + "_quality", "UNCERTAIN");
                }
            }

            tableData.add(row);
        }

        // 按采集时间倒序排序
        tableData.sort((a, b) -> {
            Date timeA = (Date) a.get("collectionTimestamp");
            Date timeB = (Date) b.get("collectionTimestamp");
            if (timeA == null && timeB == null) return 0;
            if (timeA == null) return 1;
            if (timeB == null) return -1;
            return timeB.compareTo(timeA);
        });

        return tableData;
    }

    /**
     * 转换字段值为对应的数据类型
     */
    private Object convertFieldValue(String value, String dataType) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }

        try {
            if (dataType == null) {
                return value;
            }

            switch (dataType.toLowerCase()) {
                case "int":
                case "integer":
                    return Integer.valueOf(value.trim());
                case "long":
                    return Long.valueOf(value.trim());
                case "float":
                    return Float.valueOf(value.trim());
                case "double":
                    return Double.valueOf(value.trim());
                case "boolean":
                case "bool":
                    return Boolean.valueOf(value.trim()) || "1".equals(value.trim());
                case "string":
                default:
                    return value.trim();
            }
        } catch (Exception e) {
            log.debug("字段值转换失败，返回原值: {}", value);
            return value;
        }
    }
}
