package cn.jihong.equipment.app.service.impl;

import cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO;
import cn.jihong.equipment.api.model.vo.out.DeviceFieldMappingVO;
import cn.jihong.equipment.api.service.IDeviceFieldDataRecordService;
import cn.jihong.equipment.app.mapper.DeviceFieldDataRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备字段数据记录服务实现类
 * 完全规范化设计：每个字段值单独记录，关联字典表，支持历史数据追溯
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class DeviceFieldDataRecordServiceImpl extends ServiceImpl<DeviceFieldDataRecordMapper, DeviceFieldDataRecordPO> 
        implements IDeviceFieldDataRecordService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveFieldDataRecords(Integer deviceId, String companyCode, 
                                  List<DeviceFieldMappingVO> fieldMappings, 
                                  Map<String, Object> rawDataMap, 
                                  String batchId, Date collectionTime) {
        
        if (CollectionUtils.isEmpty(fieldMappings) || rawDataMap.isEmpty()) {
            log.debug("设备[{}]字段映射或原始数据为空，跳过保存", deviceId);
            return 0;
        }

        log.debug("开始保存设备[{}]字段数据记录，字段映射数：{}，原始数据字段数：{}", 
                deviceId, fieldMappings.size(), rawDataMap.size());

        List<DeviceFieldDataRecordPO> records = new ArrayList<>();
        int processedCount = 0;
        int skippedCount = 0;

        for (DeviceFieldMappingVO mapping : fieldMappings) {
            // 只处理启用的字段
            if (!Boolean.TRUE.equals(mapping.getIsEnabled())) {
                log.debug("跳过未启用的字段映射: 字段编码[{}]", mapping.getFieldCode());
                skippedCount++;
                continue;
            }

            // 修改匹配逻辑：始终使用寄存器地址作为key进行数据匹配
            String dataKey = "register_" + mapping.getRegisterAddress();
            Object rawValue = rawDataMap.get(dataKey);
            
            // 创建字段数据记录
            DeviceFieldDataRecordPO record = createFieldDataRecord(
                    deviceId, companyCode, mapping, rawValue, batchId, collectionTime);
            
            if (record != null) {
                records.add(record);
                processedCount++;
                log.debug("创建字段数据记录：设备[{}]，寄存器[{}]，字段[{}]，值[{}]", 
                        deviceId, mapping.getRegisterAddress(), mapping.getFieldCode(), rawValue);
            }
        }

        // 批量保存记录
        int savedCount = 0;
        if (!records.isEmpty()) {
            try {
                boolean saved = super.saveBatch(records);
                if (saved) {
                    savedCount = records.size();
                    log.info("设备[{}]字段数据记录保存成功，批次ID：{}，保存记录数：{}，跳过字段数：{}", 
                            deviceId, batchId, savedCount, skippedCount);
                } else {
                    log.warn("设备[{}]字段数据记录保存失败", deviceId);
                }
            } catch (Exception e) {
                log.error("设备[{}]字段数据记录批量保存异常：{}", deviceId, e.getMessage(), e);
                throw e;
            }
        }

        return savedCount;
    }

    /**
     * 创建字段数据记录
     */
    private DeviceFieldDataRecordPO createFieldDataRecord(Integer deviceId, String companyCode,
                                                         DeviceFieldMappingVO mapping, Object rawValue,
                                                         String batchId, Date collectionTime) {
        try {
            DeviceFieldDataRecordPO record = new DeviceFieldDataRecordPO();
            
            // 基本信息
            record.setDeviceId(deviceId);
            record.setCompanyCode(companyCode);
            record.setCollectionBatchId(batchId);
            record.setCollectionTimestamp(collectionTime);
            record.setProcessedTimestamp(new Date());
            
            // 字段信息（关联字典表 + 冗余存储）
            record.setFieldId(mapping.getFieldId());
            record.setFieldCode(mapping.getFieldCode());
            record.setFieldName(mapping.getFieldName());
            record.setFieldCategory(mapping.getFieldCategory());
            
            // 寄存器信息（关联寄存器表 + 冗余存储）
            record.setRegisterId(mapping.getRegisterId());
            record.setRegisterAddress(mapping.getRegisterAddress());
            record.setRegisterType(mapping.getRegisterType());
            
            // 数据类型和单位
            record.setDataType(mapping.getDataType());
            record.setUnit(mapping.getUnit());
            record.setScalingFactor(mapping.getScalingFactor());
            
            // 处理字段值和数据质量
            if (rawValue != null) {
                try {
                    // 数据类型转换
                    Object convertedValue = convertDataType(rawValue, mapping.getDataType());
                    
                    // 应用缩放因子
                    if (mapping.getScalingFactor() != null && convertedValue instanceof Number) {
                        convertedValue = applyScalingFactor((Number) convertedValue, mapping.getScalingFactor());
                    }
                    
                    record.setFieldValue(convertedValue != null ? convertedValue.toString() : null);
                    record.setQuality(DeviceFieldDataRecordPO.DataQuality.GOOD.getCode());
                    
                } catch (Exception e) {
                    log.warn("字段[{}]数据转换失败：{}", mapping.getFieldCode(), e.getMessage());
                    record.setFieldValue(rawValue.toString());
                    record.setQuality(DeviceFieldDataRecordPO.DataQuality.BAD.getCode());
                    record.setErrorMessage("数据转换失败：" + e.getMessage());
                }
            } else {
                record.setFieldValue(null);
                record.setQuality(DeviceFieldDataRecordPO.DataQuality.UNCERTAIN.getCode());
                record.setErrorMessage("原始数据中未找到该字段");
            }
            
            return record;
            
        } catch (Exception e) {
            log.error("创建字段[{}]数据记录失败：{}", mapping.getFieldCode(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 数据类型转换
     */
    private Object convertDataType(Object value, String dataType) {
        if (value == null || !StringUtils.hasText(dataType)) {
            return value;
        }

        try {
            String valueStr = value.toString().trim();
            
            switch (dataType.toLowerCase()) {
                case "int":
                case "integer":
                    return Integer.valueOf(valueStr);
                case "long":
                    return Long.valueOf(valueStr);
                case "float":
                    return Float.valueOf(valueStr);
                case "double":
                    return Double.valueOf(valueStr);
                case "boolean":
                case "bool":
                    return Boolean.valueOf(valueStr) || "1".equals(valueStr) || "true".equalsIgnoreCase(valueStr);
                case "string":
                default:
                    return valueStr;
            }
        } catch (Exception e) {
            log.warn("数据类型转换失败，原值：{}，目标类型：{}", value, dataType);
            return value;
        }
    }

    /**
     * 应用缩放因子
     */
    private Number applyScalingFactor(Number value, java.math.BigDecimal scalingFactor) {
        if (value == null || scalingFactor == null) {
            return value;
        }

        try {
            java.math.BigDecimal bigDecimalValue = new java.math.BigDecimal(value.toString());
            java.math.BigDecimal result = bigDecimalValue.multiply(scalingFactor);
            
            // 根据原始数据类型返回相应类型
            if (value instanceof Integer) {
                return result.intValue();
            } else if (value instanceof Long) {
                return result.longValue();
            } else if (value instanceof Float) {
                return result.floatValue();
            } else {
                return result.doubleValue();
            }
        } catch (Exception e) {
            log.warn("应用缩放因子失败，原值：{}，缩放因子：{}", value, scalingFactor);
            return value;
        }
    }

    @Override
    public List<DeviceFieldDataRecordPO> listByDeviceId(Integer deviceId) {
        LambdaQueryWrapper<DeviceFieldDataRecordPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceFieldDataRecordPO::getDeviceId, deviceId)
                .orderByDesc(DeviceFieldDataRecordPO::getCollectionTimestamp)
                .orderByAsc(DeviceFieldDataRecordPO::getFieldCode);
        return list(queryWrapper);
    }

    @Override
    public List<DeviceFieldDataRecordPO> listByDeviceIdAndTimeRange(Integer deviceId, Date startTime, Date endTime) {
        return baseMapper.selectByDeviceIdAndTimeRange(deviceId, startTime, endTime);
    }

    @Override
    public List<DeviceFieldDataRecordPO> listByBatchId(String batchId) {
        return baseMapper.selectByBatchId(batchId);
    }

    @Override
    public List<DeviceFieldDataRecordPO> listByCompanyCode(String companyCode, Integer limit) {
        return baseMapper.selectByCompanyCode(companyCode, limit != null ? limit : 1000);
    }

    @Override
    public List<DeviceFieldDataRecordPO> getLatestByDeviceId(Integer deviceId) {
        return baseMapper.selectLatestByDeviceId(deviceId);
    }

    @Override
    public DeviceFieldDataRecordPO getLatestByDeviceIdAndFieldCode(Integer deviceId, String fieldCode) {
        return baseMapper.selectLatestByDeviceIdAndFieldCode(deviceId, fieldCode);
    }

    @Override
    public Map<String, Object> getStatsByDeviceIdAndTimeRange(Integer deviceId, Date startTime, Date endTime) {
        return baseMapper.selectStatsByDeviceIdAndTimeRange(deviceId, startTime, endTime);
    }

    @Override
    public Map<String, Object> convertRecordsToMap(List<DeviceFieldDataRecordPO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new HashMap<>();
        }

        Map<String, Object> resultMap = new HashMap<>();
        
        for (DeviceFieldDataRecordPO record : records) {
            String key = record.getFieldCode();
            if (!StringUtils.hasText(key)) {
                key = "register_" + record.getRegisterAddress();
            }
            
            // 根据数据类型转换值
            Object value = convertStringToTypedValue(record.getFieldValue(), record.getDataType());
            resultMap.put(key, value);
        }
        
        // 添加元数据
        if (!records.isEmpty()) {
            DeviceFieldDataRecordPO firstRecord = records.get(0);
            resultMap.put("_device_id", firstRecord.getDeviceId());
            resultMap.put("_collection_time", firstRecord.getCollectionTimestamp());
            resultMap.put("_batch_id", firstRecord.getCollectionBatchId());
            resultMap.put("_field_count", records.size());
        }
        
        return resultMap;
    }

    /**
     * 将字符串值转换为对应的数据类型
     */
    private Object convertStringToTypedValue(String value, String dataType) {
        if (!StringUtils.hasText(value) || !StringUtils.hasText(dataType)) {
            return value;
        }

        try {
            switch (dataType.toLowerCase()) {
                case "int":
                case "integer":
                    return Integer.valueOf(value);
                case "long":
                    return Long.valueOf(value);
                case "float":
                    return Float.valueOf(value);
                case "double":
                    return Double.valueOf(value);
                case "boolean":
                case "bool":
                    return Boolean.valueOf(value);
                case "string":
                default:
                    return value;
            }
        } catch (Exception e) {
            log.debug("字符串值转换失败，返回原值：{}", value);
            return value;
        }
    }

    @Override
    public Map<String, List<DeviceFieldDataRecordPO>> groupRecordsByBatch(List<DeviceFieldDataRecordPO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new HashMap<>();
        }
        
        return records.stream()
                .collect(Collectors.groupingBy(DeviceFieldDataRecordPO::getCollectionBatchId));
    }

    @Override
    public boolean validateRecordIntegrity(Integer deviceId, String batchId) {
        try {
            List<DeviceFieldDataRecordPO> records = listByBatchId(batchId);
            if (CollectionUtils.isEmpty(records)) {
                log.warn("批次[{}]未找到任何记录", batchId);
                return false;
            }
            
            // 验证所有记录都属于同一设备
            boolean allSameDevice = records.stream()
                    .allMatch(record -> deviceId.equals(record.getDeviceId()));
            
            if (!allSameDevice) {
                log.warn("批次[{}]中存在不同设备的记录", batchId);
                return false;
            }
            
            // 验证数据质量
            long badRecords = records.stream()
                    .filter(record -> DeviceFieldDataRecordPO.DataQuality.BAD.getCode().equals(record.getQuality()))
                    .count();
            
            if (badRecords > 0) {
                log.warn("批次[{}]中存在{}条错误记录", batchId, badRecords);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("验证批次[{}]记录完整性失败：{}", batchId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Long countByFieldId(Integer fieldId) {
        return baseMapper.countByFieldId(fieldId);
    }

    @Override
    public Long countByRegisterId(Integer registerId) {
        return baseMapper.countByRegisterId(registerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBatch(List<DeviceFieldDataRecordPO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        
        try {
            boolean saved = super.saveBatch(records);
            return saved ? records.size() : 0;
        } catch (Exception e) {
            log.error("批量保存字段数据记录失败：{}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<DeviceFieldDataRecordPO> listByDeviceIdAndFieldCodes(Integer deviceId, List<String> fieldCodes, 
                                                                     Date startTime, Date endTime, Integer limit) {
        try {
            log.debug("根据设备ID[{}]和字段编码列表查询历史数据，字段数：{}，时间范围：{} - {}，限制条数：{}", 
                    deviceId, fieldCodes != null ? fieldCodes.size() : 0, startTime, endTime, limit);
            return baseMapper.selectByDeviceIdAndFieldCodes(deviceId, fieldCodes, startTime, endTime, limit);
        } catch (Exception e) {
            log.error("根据设备ID[{}]和字段编码列表查询历史数据失败：{}", deviceId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<DeviceFieldDataRecordPO> getLatestByDeviceIdAndFieldCodes(Integer deviceId, List<String> fieldCodes) {
        try {
            log.debug("根据设备ID[{}]和字段编码列表查询最新数据，字段数：{}", 
                    deviceId, fieldCodes != null ? fieldCodes.size() : 0);
            return baseMapper.selectLatestByDeviceIdAndFieldCodes(deviceId, fieldCodes);
        } catch (Exception e) {
            log.error("根据设备ID[{}]和字段编码列表查询最新数据失败：{}", deviceId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
