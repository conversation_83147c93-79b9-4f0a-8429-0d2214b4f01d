package cn.jihong.equipment.api.service;

import cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO;
import cn.jihong.equipment.api.model.vo.out.DeviceFieldMappingVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备字段数据记录服务接口
 * 完全规范化设计：每个字段值单独记录，关联字典表，支持历史数据追溯
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface IDeviceFieldDataRecordService extends IService<DeviceFieldDataRecordPO> {

    /**
     * 保存设备字段数据记录
     * 根据字段映射关系和原始数据，创建单独的字段记录
     *
     * @param deviceId        设备ID
     * @param companyCode     公司编码
     * @param fieldMappings   字段映射关系
     * @param rawDataMap      原始数据Map
     * @param batchId         采集批次ID
     * @param collectionTime  采集时间
     * @return 保存的记录数量
     */
    int saveFieldDataRecords(Integer deviceId, String companyCode, 
                           List<DeviceFieldMappingVO> fieldMappings, 
                           Map<String, Object> rawDataMap, 
                           String batchId, Date collectionTime);

    /**
     * 根据设备ID查询字段数据记录
     *
     * @param deviceId 设备ID
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> listByDeviceId(Integer deviceId);

    /**
     * 根据设备ID和时间范围查询字段数据记录
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> listByDeviceIdAndTimeRange(Integer deviceId, Date startTime, Date endTime);

    /**
     * 根据批次ID查询字段数据记录
     *
     * @param batchId 批次ID
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> listByBatchId(String batchId);

    /**
     * 根据公司编码查询字段数据记录
     *
     * @param companyCode 公司编码
     * @param limit       限制条数
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> listByCompanyCode(String companyCode, Integer limit);

    /**
     * 获取设备最新的字段数据记录
     *
     * @param deviceId 设备ID
     * @return 最新的字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> getLatestByDeviceId(Integer deviceId);

    /**
     * 根据设备ID和字段编码获取最新的字段数据记录
     *
     * @param deviceId  设备ID
     * @param fieldCode 字段编码
     * @return 最新的字段数据记录
     */
    DeviceFieldDataRecordPO getLatestByDeviceIdAndFieldCode(Integer deviceId, String fieldCode);

    /**
     * 统计设备字段数据记录
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    Map<String, Object> getStatsByDeviceIdAndTimeRange(Integer deviceId, Date startTime, Date endTime);

    /**
     * 将字段数据记录转换为Map格式（兼容原有接口）
     *
     * @param records 字段数据记录列表
     * @return 转换后的Map数据
     */
    Map<String, Object> convertRecordsToMap(List<DeviceFieldDataRecordPO> records);

    /**
     * 将字段数据记录按批次分组
     *
     * @param records 字段数据记录列表
     * @return 按批次分组的数据
     */
    Map<String, List<DeviceFieldDataRecordPO>> groupRecordsByBatch(List<DeviceFieldDataRecordPO> records);

    /**
     * 验证字段数据记录的完整性
     *
     * @param deviceId 设备ID
     * @param batchId  批次ID
     * @return 验证结果
     */
    boolean validateRecordIntegrity(Integer deviceId, String batchId);

    /**
     * 根据字段ID查询相关的数据记录数量
     *
     * @param fieldId 字段ID
     * @return 记录数量
     */
    Long countByFieldId(Integer fieldId);

    /**
     * 根据寄存器ID查询相关的数据记录数量
     *
     * @param registerId 寄存器ID
     * @return 记录数量
     */
    Long countByRegisterId(Integer registerId);

    /**
     * 批量保存字段数据记录
     *
     * @param records 字段数据记录列表
     * @return 保存的记录数量
     */
    int saveBatch(List<DeviceFieldDataRecordPO> records);

    /**
     * 根据设备ID和字段编码列表查询历史字段数据记录
     *
     * @param deviceId   设备ID
     * @param fieldCodes 字段编码列表
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param limit      限制条数
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> listByDeviceIdAndFieldCodes(Integer deviceId, List<String> fieldCodes, 
                                                              Date startTime, Date endTime, Integer limit);

    /**
     * 根据设备ID和字段编码列表查询最新的字段数据记录
     *
     * @param deviceId   设备ID
     * @param fieldCodes 字段编码列表
     * @return 最新的字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> getLatestByDeviceIdAndFieldCodes(Integer deviceId, List<String> fieldCodes);
}
