<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.jihong.equipment.app.mapper.DeviceFieldDataRecordMapper">

    <!--
        根据设备ID和时间范围查询字段数据记录
        @param deviceId 设备ID
        @param startTime 开始时间
        @param endTime 结束时间
    -->
    <select id="selectByDeviceIdAndTimeRange" resultType="cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO">
        SELECT * FROM device_field_data_records
        WHERE deleted = 0
        <if test="deviceId != null">
            AND device_id = #{deviceId}
        </if>
        <if test="startTime != null">
            AND collection_timestamp &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND collection_timestamp &lt;= #{endTime}
        </if>
        ORDER BY collection_timestamp DESC, field_code ASC
    </select>

    <!--
        根据批次ID查询字段数据记录
        @param batchId 批次ID
    -->
    <select id="selectByBatchId" resultType="cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO">
        SELECT * FROM device_field_data_records
        WHERE collection_batch_id = #{batchId} AND deleted = 0
        ORDER BY field_code ASC
    </select>

    <!--
        根据设备ID和字段编码查询最新的字段数据记录
        @param deviceId 设备ID
        @param fieldCode 字段编码
    -->
    <select id="selectLatestByDeviceIdAndFieldCode" resultType="cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO">
        SELECT * FROM device_field_data_records
        WHERE device_id = #{deviceId} AND field_code = #{fieldCode} AND deleted = 0
        ORDER BY collection_timestamp DESC
        LIMIT 1
    </select>

    <!--
        根据设备ID查询最新的所有字段数据记录
        @param deviceId 设备ID
    -->
    <select id="selectLatestByDeviceId" resultType="cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO">
        SELECT d.* FROM device_field_data_records d
        INNER JOIN (
            SELECT device_id, field_code, MAX(collection_timestamp) as max_time
            FROM device_field_data_records
            WHERE device_id = #{deviceId} AND deleted = 0
            GROUP BY device_id, field_code
        ) latest ON d.device_id = latest.device_id
            AND d.field_code = latest.field_code
            AND d.collection_timestamp = latest.max_time
        WHERE d.deleted = 0
        ORDER BY d.field_code ASC
    </select>

    <!--
        根据公司编码查询字段数据记录
        @param companyCode 公司编码
        @param limit 限制条数
    -->
    <select id="selectByCompanyCode" resultType="cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO">
        SELECT * FROM device_field_data_records
        WHERE company_code = #{companyCode} AND deleted = 0
        ORDER BY collection_timestamp DESC
        LIMIT #{limit}
    </select>

    <!--
        统计设备字段数据记录数量
        @param deviceId 设备ID
        @param startTime 开始时间
        @param endTime 结束时间
    -->
    <select id="selectStatsByDeviceIdAndTimeRange" resultType="map">
        SELECT
            COUNT(*) as total_count,
            COUNT(DISTINCT field_code) as field_count,
            COUNT(DISTINCT collection_batch_id) as batch_count,
            SUM(CASE WHEN quality = 'GOOD' THEN 1 ELSE 0 END) as good_count,
            SUM(CASE WHEN quality = 'BAD' THEN 1 ELSE 0 END) as bad_count,
            SUM(CASE WHEN quality = 'UNCERTAIN' THEN 1 ELSE 0 END) as uncertain_count
        FROM device_field_data_records
        WHERE device_id = #{deviceId} AND deleted = 0
        <if test="startTime != null">
            AND collection_timestamp &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND collection_timestamp &lt;= #{endTime}
        </if>
    </select>

    <!--
        批量插入字段数据记录
        @param records 字段数据记录列表
    -->
    <insert id="insertBatch">
        INSERT INTO device_field_data_records
        (device_id, field_id, field_code, field_value, quality, collection_timestamp, collection_batch_id, company_code, register_id, deleted)
        VALUES
        <foreach collection="records" item="item" separator=",">
            (#{item.deviceId}, #{item.fieldId}, #{item.fieldCode}, #{item.fieldValue}, #{item.quality}, #{item.collectionTimestamp}, #{item.collectionBatchId}, #{item.companyCode}, #{item.registerId}, #{item.deleted})
        </foreach>
    </insert>

    <!--
        根据字段ID查询相关的数据记录数量
        @param fieldId 字段ID
    -->
    <select id="countByFieldId" resultType="long">
        SELECT COUNT(*) FROM device_field_data_records
        WHERE field_id = #{fieldId} AND deleted = 0
    </select>

    <!--
        根据寄存器ID查询相关的数据记录数量
        @param registerId 寄存器ID
    -->
    <select id="countByRegisterId" resultType="long">
        SELECT COUNT(*) FROM device_field_data_records
        WHERE register_id = #{registerId} AND deleted = 0
    </select>

</mapper> 