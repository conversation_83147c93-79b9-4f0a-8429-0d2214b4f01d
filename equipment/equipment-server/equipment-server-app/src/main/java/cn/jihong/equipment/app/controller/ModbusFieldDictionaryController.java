package cn.jihong.equipment.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.equipment.api.model.dto.ModbusFieldDictionaryDTO;
import cn.jihong.equipment.api.model.po.ModbusFieldDictionaryPO;
import cn.jihong.equipment.api.model.vo.in.ModbusFieldDictionaryQueryInVO;
import cn.jihong.equipment.api.model.vo.out.ModbusFieldDictionaryOutVO;
import cn.jihong.equipment.api.service.ModbusFieldDictionaryService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Modbus字段字典控制器
 */
@RestController
@RequestMapping("/modbus/field-dictionary")
@ShenyuSpringMvcClient(path = "/modbus/field-dictionary/**")
public class ModbusFieldDictionaryController {

    @Autowired
    private ModbusFieldDictionaryService modbusFieldDictionaryService;

    /**
     * 分页查询字段字典列表
     *
     * @param inVO 查询参数
     * @return 字段字典列表
     */
    @PostMapping("/page")
    public StandardResult<Pagination<ModbusFieldDictionaryOutVO>> page(@Validated @RequestBody ModbusFieldDictionaryQueryInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusFieldDictionaryService.page(inVO));
    }

    /**
     * 获取字段字典详情
     *
     * @param id 字段字典ID
     * @return 字段字典详情
     */
    @GetMapping("/{id}")
    public StandardResult<ModbusFieldDictionaryPO> getFieldDictionary(@PathVariable Integer id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusFieldDictionaryService.getFieldDictionaryById(id));
    }

    /**
     * 创建字段字典
     *
     * @param dictionaryDTO 字段字典信息
     * @return 字段字典ID
     */
    @PostMapping("/save")
    public StandardResult<Integer> createFieldDictionary(@RequestBody @Valid ModbusFieldDictionaryDTO dictionaryDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusFieldDictionaryService.createFieldDictionary(dictionaryDTO));
    }

    /**
     * 更新字段字典
     * @param dictionaryDTO 字段字典信息
     * @return 是否成功
     */
    @PostMapping("/update")
    public StandardResult<Boolean> updateFieldDictionary(@RequestBody @Valid ModbusFieldDictionaryDTO dictionaryDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusFieldDictionaryService.updateFieldDictionary(dictionaryDTO));
    }

    /**
     * 删除字段字典
     *
     * @param id 字段字典ID
     * @return 是否成功
     */
    @PostMapping("/delete/{id}")
    public StandardResult<Boolean> deleteFieldDictionary(@PathVariable Integer id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusFieldDictionaryService.deleteFieldDictionary(id, null));
    }

    /**
     * 根据类别和公司编码获取字段字典列表
     *
     * @param category 字段类别
     * @return 字段字典列表
     */
    @GetMapping("/list/category")
    public StandardResult<List<ModbusFieldDictionaryOutVO>> listByCategoryAndCompanyCode(
            @RequestParam String category) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusFieldDictionaryService.listByCategoryAndCompanyCode(category, null));
    }
} 