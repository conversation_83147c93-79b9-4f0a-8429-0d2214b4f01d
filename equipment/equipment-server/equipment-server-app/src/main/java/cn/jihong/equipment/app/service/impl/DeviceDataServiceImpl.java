package cn.jihong.equipment.app.service.impl;

import cn.jihong.common.exception.CommonException;
import cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO;
import cn.jihong.equipment.api.model.vo.DeviceFieldHistoryInVO;
import cn.jihong.equipment.api.model.vo.DeviceFieldHistoryTableVO;
import cn.jihong.equipment.api.model.vo.out.DeviceFieldDataBatchVO;
import cn.jihong.equipment.api.model.vo.out.DeviceFieldMappingVO;
import cn.jihong.equipment.api.service.FieldMappingService;
import cn.jihong.equipment.api.service.IDeviceDataService;
import cn.jihong.equipment.api.service.IDeviceFieldDataRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 设备数据服务实现类
 * 整合设备数据查询和字段映射的业务逻辑
 * 支持新的字段数据记录查询和原有的历史数据查询
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@Service
public class DeviceDataServiceImpl implements IDeviceDataService {

    @Autowired
    private FieldMappingService fieldMappingService;

    @Autowired
    private IDeviceFieldDataRecordService deviceFieldDataRecordService;

    @Override
    public List<DeviceFieldMappingVO> getDeviceFieldMapping(Long deviceId) {
        try {
            log.debug("获取设备[{}]字段映射关系", deviceId);
            return fieldMappingService.getDeviceFieldMapping(deviceId);
        } catch (Exception e) {
            log.error("获取设备[{}]字段映射失败：{}", deviceId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<DeviceFieldMappingVO> getEnabledDeviceFieldMapping(Long deviceId) {
        try {
            log.debug("获取设备[{}]启用字段映射关系", deviceId);
            return fieldMappingService.getEnabledDeviceFieldMapping(deviceId);
        } catch (Exception e) {
            log.error("获取设备[{}]启用字段映射失败：{}", deviceId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    @Override
    public List<DeviceFieldDataBatchVO> getDeviceFieldDataRecordsByBatch(Integer deviceId, String startTime, Date endTime) {
        try {
            log.debug("查询设备[{}]字段数据记录（按批次分组），时间范围：{} - {}", deviceId, startTime, endTime);
            
            // 1. 获取字段数据记录
            List<DeviceFieldDataRecordPO> records;
            if (startTime != null || endTime != null) {
                records = deviceFieldDataRecordService.listByDeviceIdAndTimeRange(deviceId, startTime, endTime);
            } else {
                records = deviceFieldDataRecordService.listByDeviceId(deviceId);
            }
            
            if (records.isEmpty()) {
                return Collections.emptyList();
            }
            
            // 2. 按批次分组
            Map<String, List<DeviceFieldDataRecordPO>> batchGroups = deviceFieldDataRecordService.groupRecordsByBatch(records);
            
            // 3. 转换为批次VO
            List<DeviceFieldDataBatchVO> batchVOs = new ArrayList<>();
            for (Map.Entry<String, List<DeviceFieldDataRecordPO>> entry : batchGroups.entrySet()) {
                String batchId = entry.getKey();
                List<DeviceFieldDataRecordPO> batchRecords = entry.getValue();
                
                DeviceFieldDataBatchVO batchVO = convertToBatchVO(batchId, batchRecords);
                if (batchVO != null) {
                    batchVOs.add(batchVO);
                }
            }
            
            // 4. 按采集时间倒序排序
            batchVOs.sort((a, b) -> b.getCollectionTimestamp().compareTo(a.getCollectionTimestamp()));
            
            log.debug("设备[{}]共查询到{}个批次的数据", deviceId, batchVOs.size());
            return batchVOs;
            
        } catch (Exception e) {
            log.error("查询设备[{}]字段数据记录（按批次分组）失败：{}", deviceId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 将批次记录转换为批次VO
     */
    private DeviceFieldDataBatchVO convertToBatchVO(String batchId, List<DeviceFieldDataRecordPO> records) {
        if (records.isEmpty()) {
            return null;
        }
        
        DeviceFieldDataRecordPO firstRecord = records.get(0);
        DeviceFieldDataBatchVO batchVO = new DeviceFieldDataBatchVO();
        
        // 设置基本信息
        batchVO.setDeviceId(firstRecord.getDeviceId());
        batchVO.setCompanyCode(firstRecord.getCompanyCode());
        batchVO.setCollectionBatchId(batchId);
        batchVO.setCollectionTimestamp(firstRecord.getCollectionTimestamp());
        batchVO.setProcessedTimestamp(firstRecord.getProcessedTimestamp());
        batchVO.setFieldCount(records.size());
        
        // 转换字段数据
        Map<String, DeviceFieldDataBatchVO.FieldValueInfo> fieldData = new LinkedHashMap<>();
        DeviceFieldDataBatchVO.QualityStats qualityStats = new DeviceFieldDataBatchVO.QualityStats();
        
        for (DeviceFieldDataRecordPO record : records) {
            // 确定字段key
            String fieldKey = StringUtils.hasText(record.getFieldCode()) ? 
                    record.getFieldCode() : "register_" + record.getRegisterAddress();
            
            // 创建字段值信息
            DeviceFieldDataBatchVO.FieldValueInfo fieldInfo = new DeviceFieldDataBatchVO.FieldValueInfo();
            fieldInfo.setFieldId(record.getFieldId());
            fieldInfo.setFieldCode(record.getFieldCode());
            fieldInfo.setFieldName(record.getFieldName());
            fieldInfo.setFieldCategory(record.getFieldCategory());
            fieldInfo.setRegisterAddress(record.getRegisterAddress());
            fieldInfo.setFieldValue(record.getFieldValue());
            fieldInfo.setDataType(record.getDataType());
            fieldInfo.setUnit(record.getUnit());
            fieldInfo.setQuality(record.getQuality());
            fieldInfo.setErrorMessage(record.getErrorMessage());
            
            fieldData.put(fieldKey, fieldInfo);
            
            // 统计数据质量
            String quality = record.getQuality();
            qualityStats.setTotalCount(qualityStats.getTotalCount() + 1);
            if ("GOOD".equals(quality)) {
                qualityStats.setGoodCount(qualityStats.getGoodCount() + 1);
            } else if ("BAD".equals(quality)) {
                qualityStats.setBadCount(qualityStats.getBadCount() + 1);
            } else if ("UNCERTAIN".equals(quality)) {
                qualityStats.setUncertainCount(qualityStats.getUncertainCount() + 1);
            }
        }
        
        batchVO.setFieldData(fieldData);
        batchVO.setQualityStats(qualityStats);
        
        return batchVO;
    }

    @Override
    public List<DeviceFieldDataRecordPO> getDeviceFieldDataRecords(Integer deviceId, String startTime, Date endTime) {
        try {
            log.debug("查询设备[{}]字段数据记录，时间范围：{} - {}", deviceId, startTime, endTime);
            
            if (startTime != null || endTime != null) {
                return deviceFieldDataRecordService.listByDeviceIdAndTimeRange(deviceId, startTime, endTime);
            } else {
                return deviceFieldDataRecordService.listByDeviceId(deviceId);
            }
        } catch (Exception e) {
            log.error("查询设备[{}]字段数据记录失败：{}", deviceId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<DeviceFieldDataRecordPO> getLatestDeviceFieldData(Integer deviceId) {
        try {
            log.debug("获取设备[{}]最新字段数据记录", deviceId);
            return deviceFieldDataRecordService.getLatestByDeviceId(deviceId);
        } catch (Exception e) {
            log.error("获取设备[{}]最新字段数据记录失败：{}", deviceId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<DeviceFieldDataRecordPO> getFieldDataByBatchId(String batchId) {
        try {
            log.debug("根据批次ID[{}]查询字段数据记录", batchId);
            return deviceFieldDataRecordService.listByBatchId(batchId);
        } catch (Exception e) {
            log.error("根据批次ID[{}]查询字段数据记录失败：{}", batchId, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<DeviceFieldDataRecordPO> getCompanyFieldDataRecords(String companyCode, Integer limit) {
        try {
            log.debug("查询公司[{}]字段数据记录，限制条数：{}", companyCode, limit);
            return deviceFieldDataRecordService.listByCompanyCode(companyCode, limit);
        } catch (Exception e) {
            log.error("查询公司[{}]字段数据记录失败：{}", companyCode, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public Map<String, Object> getDeviceFieldDataStats(Integer deviceId, String startTime, Date endTime) {
        try {
            log.debug("获取设备[{}]字段数据统计信息，时间范围：{} - {}", deviceId, startTime, endTime);
            return deviceFieldDataRecordService.getStatsByDeviceIdAndTimeRange(deviceId, startTime, endTime);
        } catch (Exception e) {
            log.error("获取设备[{}]字段数据统计信息失败：{}", deviceId, e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> convertFieldRecordsToMap(List<DeviceFieldDataRecordPO> records) {
        try {
            log.debug("转换字段数据记录为Map格式，记录数：{}", records.size());
            return deviceFieldDataRecordService.convertRecordsToMap(records);
        } catch (Exception e) {
            log.error("转换字段数据记录为Map格式失败：{}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

    @Override
    public DeviceFieldDataRecordPO getLatestFieldValue(Integer deviceId, String fieldCode) {
        try {
            log.debug("获取设备[{}]字段[{}]最新值", deviceId, fieldCode);
            return deviceFieldDataRecordService.getLatestByDeviceIdAndFieldCode(deviceId, fieldCode);
        } catch (Exception e) {
            log.error("获取设备[{}]字段[{}]最新值失败：{}", deviceId, fieldCode, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public DeviceFieldHistoryTableVO getDeviceFieldHistoryTable(DeviceFieldHistoryInVO inVO) {
        // 1. 校验参数
        if (inVO == null || inVO.getFieldCodes() == null || inVO.getFieldCodes().isEmpty()) {
            throw new CommonException("字段编码列表不能为空");
        }
        if (inVO.getStartTime() == null || inVO.getEndTime() == null) {
            throw new CommonException("起止时间不能为空");
        }
//        long diff = inVO.getEndTime(). - inVO.getStartTime();
        long diff = (inVO.getEndTime().getTime() - inVO.getStartTime().getTime()) / (24 * 60 * 60 * 1000);
        if (diff < 0 || diff > 7) {
            throw new CommonException("日期区间不能超过7天");
        }
        // 2. 查询数据
        List<DeviceFieldDataRecordPO> records =
                deviceFieldDataRecordService.listByDeviceIdAndTimeRange(inVO.getDeviceId(),
                        inVO.getStartTime(), inVO.getEndTime());

        // 3. 过滤字段
        List<DeviceFieldDataRecordPO> filtered = new ArrayList<>();
        for (DeviceFieldDataRecordPO rec : records) {
            if (inVO.getFieldCodes().contains(rec.getFieldCode())) {
                filtered.add(rec);
            }
        }
        // 4. 按采集时间分组
        Map<Date, List<DeviceFieldDataRecordPO>> timeGroup = new TreeMap<>();
        for (DeviceFieldDataRecordPO rec : filtered) {
            timeGroup.computeIfAbsent(rec.getCollectionTimestamp(), k -> new ArrayList<>()).add(rec);
        }
        // 5. 组装表头
        List<String> headers = new ArrayList<>();
        headers.add("采集时间");
        if (inVO.getDeviceId() == null) {
            headers.add("设备ID");
        }
        headers.addAll(inVO.getFieldCodes());
        // 6. 组装数据体
        List<Map<String, Object>> rows = new ArrayList<>();
        for (Map.Entry<Date, List<DeviceFieldDataRecordPO>> entry : timeGroup.entrySet()) {
            Map<String, Object> row = new LinkedHashMap<>();
            row.put("采集时间", entry.getKey());
            if (inVO.getDeviceId() == null) {
                // 多设备时加设备ID
                if (!entry.getValue().isEmpty()) {
                    row.put("设备ID", entry.getValue().get(0).getDeviceId());
                }
            }
            for (String code : inVO.getFieldCodes()) {
                Object val = null;
                for (DeviceFieldDataRecordPO rec : entry.getValue()) {
                    if (code.equals(rec.getFieldCode())) {
                        val = rec.getFieldValue();
                        break;
                    }
                }
                row.put(code, val);
            }
            rows.add(row);
        }
        // 7. 返回VO
        DeviceFieldHistoryTableVO vo = new DeviceFieldHistoryTableVO();
        vo.setHeaders(headers);
        vo.setRecords(rows);
        return vo;
    }

}