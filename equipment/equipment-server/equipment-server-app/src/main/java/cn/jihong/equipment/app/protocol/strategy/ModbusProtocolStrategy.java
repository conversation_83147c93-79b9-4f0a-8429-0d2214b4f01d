package cn.jihong.equipment.app.protocol.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.equipment.api.enums.ProtocolType;
import cn.jihong.equipment.api.model.dto.ExternalDataResponseDTO;
import cn.jihong.equipment.api.model.dto.ModbusRegisterDTO;
import cn.jihong.equipment.api.model.dto.RegisterWithValueDTO;
import cn.jihong.equipment.api.model.po.DevicePO;
import cn.jihong.equipment.api.model.po.ModbusRegisterPO;
import cn.jihong.equipment.api.model.po.ModbusTemplatePO;
import cn.jihong.equipment.api.model.vo.out.ModbusDeviceDetailsVO;
import cn.jihong.equipment.api.service.IDeviceService;
import cn.jihong.equipment.api.service.ModbusRegisterService;
import cn.jihong.equipment.api.service.ModbusTemplateService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * Modbus协议处理策略实现
 *
 * <AUTHOR>
 * @since 2024-05-13
 */
@Component
public class ModbusProtocolStrategy implements DeviceProtocolStrategy {

    private static final Logger log = LoggerFactory.getLogger(ModbusProtocolStrategy.class);

    @Autowired
    private IDeviceService deviceService;
    
    @Autowired
    private ModbusTemplateService modbusTemplateService;
    
    @Autowired
    private ModbusRegisterService modbusRegisterService;
    @DubboReference
    private IDataAcquisitionService dataAcquisitionService;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final Random random = new Random();

    @Override
    public StandardResult<Boolean> connect(Long deviceId) {
        log.info("Modbus协议连接设备: {}", deviceId);
        DevicePO device = deviceService.getBaseMapper().selectById(deviceId);
        if (device == null) {
            return StandardResult.resultCode(OperateCode.FAIL, Boolean.FALSE);
        }
        
        try {
            // 实现Modbus协议连接逻辑
            log.info("连接Modbus设备: {}", deviceId);
            // TODO: 实现Modbus连接逻辑
            
            return StandardResult.resultCode(OperateCode.SUCCESS, Boolean.TRUE);
        } catch (Exception e) {
            log.error("Modbus设备连接失败: {}", e.getMessage(), e);
            return StandardResult.resultCode(OperateCode.FAIL, Boolean.FALSE);
        }
    }

    @Override
    public StandardResult<Boolean> disconnect(Long deviceId) {
        log.info("Modbus协议断开设备连接: {}", deviceId);
        try {
            // 实现Modbus协议断开连接逻辑
            // TODO: 实现Modbus断开连接逻辑
            
            return StandardResult.resultCode(OperateCode.SUCCESS, Boolean.TRUE);
        } catch (Exception e) {
            log.error("Modbus设备断开连接失败: {}", e.getMessage(), e);
            return StandardResult.resultCode(OperateCode.FAIL, Boolean.FALSE);
        }
    }

    @Override
    public StandardResult<Object> read(Long deviceId, String address) {
        log.info("Modbus协议读取设备数据: {}, 地址: {}", deviceId, address);
        DevicePO device = deviceService.getBaseMapper().selectById(deviceId);
        if (device == null) {
            log.error("设备[{}]不存在", deviceId);
            return StandardResult.resultCode(OperateCode.FAIL, null);
        }
        
        try {
            // 1. 获取设备详情
            StandardResult<Object> deviceDetailsResult = getDeviceDetails(deviceId);
            if (!Integer.valueOf(OperateCode.SUCCESS.code()).equals(deviceDetailsResult.getCode())) {
                log.error("获取设备[{}]详情失败", deviceId);
                return StandardResult.resultCode(OperateCode.FAIL, null);
            }

            // 2. 调用外部接口获取带value的数据
            // 测试阶段使用模拟数据，正式环境替换为真实外部接口调用
//            ExternalDataResponseDTO externalResponse = callExternalInterfaceForTest(deviceDetailsResult.getData());
            
            if (externalResponse == null || !Boolean.TRUE.equals(externalResponse.getState())) {
                log.error("外部接口调用失败，设备ID: {}", deviceId);
                return StandardResult.resultCode(OperateCode.FAIL, null);
            }

            // 3. 转换为标准数据格式
            Map<String, Object> resultData = convertExternalDataToMap(externalResponse);
            
            log.info("设备[{}]数据读取成功，获取到{}个字段", deviceId, resultData.size());
            return StandardResult.resultCode(OperateCode.SUCCESS, resultData);
            
        } catch (Exception e) {
            log.error("Modbus设备读取数据失败: {}", e.getMessage(), e);
            return StandardResult.resultCode(OperateCode.FAIL, null);
        }
    }

    /**
     * 测试方法：模拟外部接口调用
     * 正式环境中替换为真实的外部接口调用
     *
     * @param deviceDetails 设备详情数据
     * @return 模拟的外部接口响应
     */
    private ExternalDataResponseDTO callExternalInterfaceForTest(Object deviceDetails) {
        try {
            log.info("【测试模式】模拟调用外部接口获取设备数据");
            
            // 将设备详情转换为JSON字符串，模拟发送给外部接口
            String deviceDetailsJson = objectMapper.writeValueAsString(deviceDetails);
            log.debug("发送给外部接口的数据: {}", deviceDetailsJson);
            
            // 解析设备详情，获取寄存器列表
            ModbusDeviceDetailsVO deviceDetailsVO = objectMapper.convertValue(deviceDetails, ModbusDeviceDetailsVO.class);
            
            // 创建模拟响应
            ExternalDataResponseDTO response = new ExternalDataResponseDTO();
            response.setState(true);
            response.setMessage("操作成功");
            response.setCode(200);
            
            // 创建响应数据
            ExternalDataResponseDTO.DeviceDataDTO responseData = new ExternalDataResponseDTO.DeviceDataDTO();
            BeanUtil.copyProperties(deviceDetailsVO, responseData);
            
            // 为每个寄存器生成模拟值
            List<RegisterWithValueDTO> registersWithValue = new ArrayList<>();
            if (deviceDetailsVO.getRegisters() != null) {
                for (ModbusRegisterDTO register : deviceDetailsVO.getRegisters()) {
                    RegisterWithValueDTO registerWithValue = new RegisterWithValueDTO();
                    BeanUtil.copyProperties(register, registerWithValue);
                    
                    // 根据数据类型生成模拟值
                    Object mockValue = generateMockValue(register.getDataType(), register.getAddress());
                    registerWithValue.setValue(mockValue);
                    registerWithValue.setTimestamp(System.currentTimeMillis());
                    registerWithValue.setQuality("GOOD");
                    
                    registersWithValue.add(registerWithValue);
                    log.debug("寄存器[{}]模拟值: {}", register.getAddress(), mockValue);
                }
            }
            
            responseData.setRegisters(registersWithValue);
            response.setData(responseData);
            
            log.info("【测试模式】外部接口模拟响应生成完成，包含{}个寄存器数据", registersWithValue.size());
            return response;
            
        } catch (Exception e) {
            log.error("模拟外部接口调用失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据数据类型生成模拟值
     *
     * @param dataType 数据类型
     * @param address  寄存器地址（用于生成不同的值）
     * @return 模拟值
     */
    private Object generateMockValue(String dataType, Integer address) {
        // 使用地址作为随机种子，确保相同地址生成相对稳定的值
        Random addressRandom = new Random(address);
        
        switch (dataType.toLowerCase()) {
            case "float":
                // 生成0-100之间的浮点数
                return BigDecimal.valueOf(addressRandom.nextFloat() * 100).setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
            case "double":
                return BigDecimal.valueOf(addressRandom.nextDouble() * 100).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            case "int":
            case "integer":
                return addressRandom.nextInt(1000);
            case "long":
                return addressRandom.nextLong() % 10000;
            case "boolean":
            case "bool":
                return addressRandom.nextBoolean();
            case "string":
            case "text":
            default:
                return "模拟值_" + address + "_" + System.currentTimeMillis() % 1000;
        }
    }

    /**
     * 将外部接口返回的数据转换为Map格式
     * 供后续数据处理使用
     *
     * @param externalResponse 外部接口响应
     * @return 转换后的Map数据
     */
    private Map<String, Object> convertExternalDataToMap(ExternalDataResponseDTO externalResponse) {
        Map<String, Object> resultMap = new HashMap<>();
        
        if (externalResponse.getData() != null && externalResponse.getData().getRegisters() != null) {
            for (RegisterWithValueDTO register : externalResponse.getData().getRegisters()) {
                // 使用寄存器地址作为key
                String key = "register_" + register.getAddress();
                resultMap.put(key, register.getValue());
                
                log.debug("转换寄存器数据: {} = {}", key, register.getValue());
            }
        }
        
        // 添加元数据
        resultMap.put("_collection_time", System.currentTimeMillis());
        resultMap.put("_device_id", externalResponse.getData().getId());
        resultMap.put("_external_response", true);
        
        return resultMap;
    }

    /**
     * TODO: 正式环境中的外部接口调用方法
     * 替换callExternalInterfaceForTest方法
     *
     * @param deviceDetails 设备详情
     * @return 外部接口响应
     */
    private ExternalDataResponseDTO callExternalInterface(Object deviceDetails) {
        // TODO: 实现真实的外部接口调用
        // 1. 将deviceDetails转换为外部接口需要的格式
        // 2. 发送HTTP请求到外部接口
        // 3. 解析响应数据
        // 4. 返回ExternalDataResponseDTO
        return null;
    }

    @Override
    public StandardResult<Boolean> write(Long deviceId, String address, Object value) {
        log.info("Modbus协议写入设备数据: {}, 地址: {}, 值: {}", deviceId, address, value);
        DevicePO device = deviceService.getBaseMapper().selectById(deviceId);
        if (device == null) {
            return StandardResult.resultCode(OperateCode.FAIL, Boolean.FALSE);
        }
        
        try {
            // 实现Modbus协议写入数据逻辑
            // TODO: 实现Modbus写入数据逻辑
            
            return StandardResult.resultCode(OperateCode.SUCCESS, Boolean.TRUE);
        } catch (Exception e) {
            log.error("Modbus设备写入数据失败: {}", e.getMessage(), e);
            return StandardResult.resultCode(OperateCode.FAIL, Boolean.FALSE);
        }
    }
    
    @Override
    public StandardResult<Object> getDeviceDetails(Long deviceId) {
        log.info("获取Modbus设备详情: {}", deviceId);
        DevicePO device = deviceService.getBaseMapper().selectById(deviceId);
        if (device == null) {
            return StandardResult.resultCode(OperateCode.FAIL, null);
        }
        
        try {
            // 创建Modbus设备详情对象
            ModbusDeviceDetailsVO detailsVO = new ModbusDeviceDetailsVO();
            // 复制设备基本信息
            BeanUtil.copyProperties(device, detailsVO);
            
            // 获取Modbus模板信息
            Long modbusTemplateId = device.getModbusTemplateId();
            if (modbusTemplateId != null) {
                ModbusTemplatePO template = modbusTemplateService.getTemplateById(modbusTemplateId.intValue());
                if (template != null) {
                    detailsVO.setModbusTemplateId(template.getId());
                    detailsVO.setModbusTemplateName(template.getName());
                    detailsVO.setModbusTemplateVersion(template.getVersion());
                    detailsVO.setModbusTemplateDescription(template.getDescription());
                    
                    // 获取寄存器列表
                    List<ModbusRegisterPO> registers = modbusRegisterService.listByTemplateId(template.getId());
                    if (registers != null && !registers.isEmpty()) {
                        List<ModbusRegisterDTO> registerDTOs = new ArrayList<>();
                        for (ModbusRegisterPO register : registers) {
                            ModbusRegisterDTO dto = new ModbusRegisterDTO();
                            BeanUtil.copyProperties(register, dto);
                            registerDTOs.add(dto);
                        }
                        detailsVO.setRegisters(registerDTOs);
                    }
                }
            }
            
            return StandardResult.resultCode(OperateCode.SUCCESS, detailsVO);
        } catch (Exception e) {
            log.error("获取Modbus设备详情失败: {}", e.getMessage(), e);
            return StandardResult.resultCode(OperateCode.FAIL, null);
        }
    }

    @Override
    public String getProtocolType() {
        return ProtocolType.MODBUS.name();
    }
}