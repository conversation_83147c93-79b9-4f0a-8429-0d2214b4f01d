package cn.jihong.equipment.app.mapper;

import cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备字段数据记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Mapper
public interface DeviceFieldDataRecordMapper extends BaseMapper<DeviceFieldDataRecordPO> {

    /**
     * 根据设备ID和时间范围查询字段数据记录
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> selectByDeviceIdAndTimeRange(
            @Param("deviceId") Integer deviceId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    /**
     * 根据批次ID查询字段数据记录
     *
     * @param batchId 批次ID
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> selectByBatchId(@Param("batchId") String batchId);

    /**
     * 根据设备ID和字段编码查询最新的字段数据记录
     *
     * @param deviceId  设备ID
     * @param fieldCode 字段编码
     * @return 最新的字段数据记录
     */
    DeviceFieldDataRecordPO selectLatestByDeviceIdAndFieldCode(
            @Param("deviceId") Integer deviceId,
            @Param("fieldCode") String fieldCode);

    /**
     * 根据设备ID查询最新的所有字段数据记录
     *
     * @param deviceId 设备ID
     * @return 最新的字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> selectLatestByDeviceId(@Param("deviceId") Integer deviceId);

    /**
     * 根据公司编码查询字段数据记录
     *
     * @param companyCode 公司编码
     * @param limit       限制条数
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> selectByCompanyCode(
            @Param("companyCode") String companyCode,
            @Param("limit") Integer limit);

    /**
     * 统计设备字段数据记录数量
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    Map<String, Object> selectStatsByDeviceIdAndTimeRange(
            @Param("deviceId") Integer deviceId,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);

    /**
     * 批量插入字段数据记录
     *
     * @param records 字段数据记录列表
     * @return 插入的记录数
     */
    int insertBatch(@Param("records") List<DeviceFieldDataRecordPO> records);

    /**
     * 根据字段ID查询相关的数据记录数量
     *
     * @param fieldId 字段ID
     * @return 记录数量
     */
    Long countByFieldId(@Param("fieldId") Integer fieldId);

    /**
     * 根据寄存器ID查询相关的数据记录数量
     *
     * @param registerId 寄存器ID
     * @return 记录数量
     */
    Long countByRegisterId(@Param("registerId") Integer registerId);
} 