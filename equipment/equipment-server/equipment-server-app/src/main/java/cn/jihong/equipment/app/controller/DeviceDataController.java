package cn.jihong.equipment.app.controller;

import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO;
import cn.jihong.equipment.api.model.vo.DeviceFieldHistoryInVO;
import cn.jihong.equipment.api.model.vo.DeviceFieldHistoryTableVO;
import cn.jihong.equipment.api.model.vo.out.DeviceFieldDataBatchVO;
import cn.jihong.equipment.api.service.DataProcessingService;
import cn.jihong.equipment.api.service.IDeviceDataService;
import cn.jihong.equipment.api.service.IDeviceFieldDataRecordService;
import cn.jihong.equipment.app.protocol.factory.DeviceProtocolFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * 设备数据控制器
 * 提供设备数据查询功能，支持新的字段数据记录查询和原有的历史数据查询
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/device-data")
@ShenyuSpringMvcClient(path = "/device-data/**")
public class DeviceDataController {

    private static final Logger log = LoggerFactory.getLogger(DeviceDataController.class);

    @Autowired
    private IDeviceDataService deviceDataService;



    @Autowired
    private IDeviceFieldDataRecordService deviceFieldDataRecordService;

    @Autowired
    private DataProcessingService dataProcessingService;

    @Autowired
    private DeviceProtocolFactory protocolFactory;


    /**
     * 查询设备字段数据记录（按批次分组）
     */
    @GetMapping("/field-records/{deviceId}")
    public StandardResult<List<DeviceFieldDataBatchVO>> getDeviceFieldDataRecords(
            @PathVariable Integer deviceId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getDeviceFieldDataRecordsByBatch(deviceId, startTime, endTime));
    }

    /**
     * 查询设备字段数据记录（原始格式，每个字段一条记录）
     */
    @GetMapping("/field-records/raw/{deviceId}")
    public StandardResult<List<DeviceFieldDataRecordPO>> getDeviceFieldDataRecordsRaw(
            @PathVariable Integer deviceId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getDeviceFieldDataRecords(deviceId, startTime, endTime));
    }

    /**
     * 获取设备最新字段数据记录
     */
    @GetMapping("/field-records/latest/{deviceId}")
    public StandardResult<List<DeviceFieldDataRecordPO>> getLatestDeviceFieldData(@PathVariable Integer deviceId) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getLatestDeviceFieldData(deviceId));
    }

    /**
     * 根据批次ID查询字段数据记录
     */
    @GetMapping("/field-records/batch/{batchId}")
    public StandardResult<List<DeviceFieldDataRecordPO>> getFieldDataByBatchId(@PathVariable String batchId) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getFieldDataByBatchId(batchId));
    }

    /**
     * 根据公司编码查询字段数据记录
     */
    @GetMapping("/field-records/company/{companyCode}")
    public StandardResult<List<DeviceFieldDataRecordPO>> getCompanyFieldDataRecords(
            @PathVariable String companyCode,
            @RequestParam(defaultValue = "1000") Integer limit) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getCompanyFieldDataRecords(companyCode, limit));
    }

    /**
     * 获取设备字段数据统计信息
     */
    @GetMapping("/field-records/stats/{deviceId}")
    public StandardResult<Map<String, Object>> getDeviceFieldDataStats(
            @PathVariable Integer deviceId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        Date start = parseDateString(startTime, false);
        Date end = parseDateString(endTime, true);
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getDeviceFieldDataStats(deviceId, start, end));
    }

    /**
     * 获取设备最新字段数据（Map格式，兼容原有接口）
     */
    @GetMapping("/field-records/latest-map/{deviceId}")
    public StandardResult<Map<String, Object>> getLatestDeviceFieldDataAsMap(@PathVariable Integer deviceId) {
        List<DeviceFieldDataRecordPO> records = deviceDataService.getLatestDeviceFieldData(deviceId);
        Map<String, Object> dataMap = deviceDataService.convertFieldRecordsToMap(records);
        return StandardResult.resultCode(OperateCode.SUCCESS, dataMap);
    }

    /**
     * 根据设备ID和字段编码获取最新字段值
     */
    @GetMapping("/field-records/latest/{deviceId}/{fieldCode}")
    public StandardResult<DeviceFieldDataRecordPO> getLatestFieldValue(
            @PathVariable Integer deviceId,
            @PathVariable String fieldCode) {
        return StandardResult.resultCode(OperateCode.SUCCESS, 
                deviceDataService.getLatestFieldValue(deviceId, fieldCode));
    }

    /**
     * 批量查询设备字段历史数据（表格结构）
     * @param inVO 查询参数
     * @return 表格结构
     */
    @PostMapping("/field-records/history")
    public StandardResult<DeviceFieldHistoryTableVO> getDeviceFieldHistoryTable(@RequestBody DeviceFieldHistoryInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, deviceDataService.getDeviceFieldHistoryTable(inVO));
    }

    private static Date parseDateString(String dateStr, boolean endOfDay) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            if (dateStr.length() == 10) { // yyyy-MM-dd
                String full = endOfDay ? dateStr + " 23:59:59" : dateStr + " 00:00:00";
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(full);
            } else if (dateStr.length() == 19) { // yyyy-MM-dd HH:mm:ss
                return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateStr);
            }
        } catch (ParseException e) {
            log.warn("日期参数解析失败: {}", dateStr, e);
        }
        return null;
    }

}