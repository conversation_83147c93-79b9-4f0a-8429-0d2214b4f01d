package cn.jihong.equipment.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.equipment.api.model.dto.ModbusTemplateDTO;
import cn.jihong.equipment.api.model.dto.ModbusTemplateWithRegistersDTO;
import cn.jihong.equipment.api.model.po.ModbusRegisterPO;
import cn.jihong.equipment.api.model.po.ModbusTemplatePO;
import cn.jihong.equipment.api.model.vo.in.ModbusTemplateCopyInVO;
import cn.jihong.equipment.api.model.vo.in.ModbusTemplateQueryInVO;
import cn.jihong.equipment.api.model.vo.out.ModbusTemplateOutVO;
import cn.jihong.equipment.api.service.ModbusTemplateService;
import cn.jihong.equipment.api.service.ModbusRegisterService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Modbus模板管理控制器
 */
@RestController
@RequestMapping("/modbus/template")
@ShenyuSpringMvcClient(path = "/modbus/template/**")
public class ModbusTemplateController {

    @Autowired
    private ModbusTemplateService modbusTemplateService;
    
    @Autowired
    private ModbusRegisterService modbusRegisterService;

    /**
     * 分页查询模板列表
     *
     * @param inVO 查询参数
     * @return 模板列表
     */
    @PostMapping("/page")
    public StandardResult<Pagination<ModbusTemplateOutVO>> page(@Validated @RequestBody ModbusTemplateQueryInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusTemplateService.page(inVO));
    }

    /**
     * 获取模板详情
     *
     * @param id 模板ID
     * @return 模板详情
     */
    @GetMapping("/{id}")
    public StandardResult<ModbusTemplatePO> getTemplate(@PathVariable Integer id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusTemplateService.getTemplateById(id));
    }

    /**
     * 创建模板
     *
     * @param templateDTO 模板信息
     * @return 模板ID
     */
    @PostMapping("/save")
    public StandardResult<Integer> createTemplate(@RequestBody @Valid ModbusTemplateDTO templateDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusTemplateService.createTemplate(templateDTO));
    }

    /**
     * 更新模板
     * @param templateDTO 模板信息
     * @return 是否成功
     */
    @PostMapping("/update")
    public StandardResult<Boolean> updateTemplate(@RequestBody @Valid ModbusTemplateDTO templateDTO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusTemplateService.updateTemplate(templateDTO));
    }

    /**
     * 删除模板
     *
     * @param id 模板ID
     * @return 是否成功
     */
    @PostMapping("/{id}")
    public StandardResult<Boolean> deleteTemplate(@PathVariable Integer id) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusTemplateService.deleteTemplate(id, null));
    }

    /**
     * 复制模板
     *
     * @param inVO 复制模板参数
     * @return 新模板ID
     */
    @PostMapping("/copy")
    public StandardResult<Integer> copyTemplate(@Validated @RequestBody ModbusTemplateCopyInVO inVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusTemplateService.copyTemplate(inVO.getSourceId(), inVO.getNewName(), null));
    }

    /**
     * 获取模板的寄存器列表
     *
     * @param templateId 模板ID
     * @return 寄存器列表
     */
    @GetMapping("/{templateId}/registers")
    public StandardResult<List<ModbusRegisterPO>> listRegisters(@PathVariable Integer templateId) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusRegisterService.listByTemplateId(templateId));
    }
    
    /**
     * 创建模板并保存寄存器列表
     *
     * @param dto 模板和寄存器信息
     * @return 模板ID
     */
    @PostMapping("/with-registers")
    public StandardResult<Integer> createTemplateWithRegisters(@Validated @RequestBody ModbusTemplateWithRegistersDTO dto) {
        return StandardResult.resultCode(OperateCode.SUCCESS, modbusTemplateService.createTemplateWithRegisters(dto));
    }
} 