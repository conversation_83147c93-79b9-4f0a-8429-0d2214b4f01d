package cn.jihong.equipment.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.equipment.api.model.dto.ModbusRegisterDTO;
import cn.jihong.equipment.api.model.dto.ModbusTemplateDTO;
import cn.jihong.equipment.api.model.dto.ModbusTemplateWithRegistersDTO;
import cn.jihong.equipment.api.model.po.ModbusRegisterPO;
import cn.jihong.equipment.api.model.po.ModbusTemplatePO;
import cn.jihong.equipment.api.model.vo.in.ModbusTemplateQueryInVO;
import cn.jihong.equipment.api.model.vo.out.ModbusTemplateOutVO;
import cn.jihong.equipment.api.service.ModbusTemplateService;
import cn.jihong.equipment.app.mapper.ModbusRegisterMapper;
import cn.jihong.equipment.app.mapper.ModbusTemplateMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Modbus模板服务实现类
 */
@Slf4j
@Service
public class ModbusTemplateServiceImpl extends ServiceImpl<ModbusTemplateMapper, ModbusTemplatePO> implements ModbusTemplateService {

    @Autowired
    private ModbusRegisterMapper modbusRegisterMapper;

    @Override
    public Pagination<ModbusTemplateOutVO> page(ModbusTemplateQueryInVO inVO) {
        try {
            log.debug("分页查询模板列表");
            LambdaQueryWrapper<ModbusTemplatePO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ModbusTemplatePO::getCompanyCode, SecurityUtil.getCompanySite())
                .like(StringUtils.hasLength(inVO.getName()), ModbusTemplatePO::getName, inVO.getName())
                .orderByDesc(ModbusTemplatePO::getCreateTime);

            Page<ModbusTemplatePO> page = page(new Page<>(inVO.getPageNum(), inVO.getPageSize()), queryWrapper);
            if (CollectionUtil.isEmpty(page.getRecords())) {
                return Pagination.newInstance(null);
            }

            List<ModbusTemplateOutVO> records = BeanUtil.copyToList(page.getRecords(), ModbusTemplateOutVO.class);
            return Pagination.newInstance(records, page.getTotal(), page.getPages());
        } catch (Exception e) {
            log.error("分页查询模板列表失败：{}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public ModbusTemplatePO getTemplateById(Integer id) {
        try {
            log.debug("获取模板详情，ID：{}", id);
            return getById(id);
        } catch (Exception e) {
            log.error("获取模板[{}]详情失败：{}", id, e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createTemplate(ModbusTemplateDTO dto) {
        try {
            log.debug("创建模板，名称：{}", dto.getName());
            
            // 设置公司编码
            if (dto.getCompanyCode() == null) {
                dto.setCompanyCode(SecurityUtil.getCompanySite());
            }
            
            // 检查模板名称是否已存在
            QueryWrapper<ModbusTemplatePO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", dto.getName())
                    .eq("company_code", dto.getCompanyCode())
                    .eq("deleted", 0);
            boolean exists = baseMapper.exists(queryWrapper);
            if (exists) {
                throw new CommonException("模板名称已存在，请更换名称");
            }
            
            // 将 dto 拷贝到新对象中，而不是直接修改原 dto
            ModbusTemplateDTO newDto = new ModbusTemplateDTO();
            BeanUtils.copyProperties(dto, newDto);
            
            // 设置时间和删除标记
            Date now = new Date();
            newDto.setCreateTime(now);
            newDto.setUpdateTime(now);
            newDto.setDeleted(0);
            
            // 创建模板并复制所有属性
            ModbusTemplatePO template = new ModbusTemplatePO();
            BeanUtils.copyProperties(newDto, template);
            
            // 保存模板
            save(template);
            return template.getId();
        } catch (Exception e) {
            log.error("创建模板失败：{}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTemplate(ModbusTemplateDTO dto) {
        try {
            log.debug("更新模板，ID：{}", dto.getId());
            
            // 获取原模板
            ModbusTemplatePO template = getById(dto.getId());
            if (template == null) {
                return false;
            }
            
            // 如果名称有变更，检查新名称是否已存在
            if (!template.getName().equals(dto.getName())) {
                QueryWrapper<ModbusTemplatePO> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("name", dto.getName())
                        .eq("company_code", dto.getCompanyCode())
                        .eq("deleted", 0)
                        .ne("id", dto.getId());
                boolean exists = baseMapper.exists(queryWrapper);
                if (exists) {
                    throw new CommonException("模板名称已存在，请更换名称");
                }
            }
            
            // 更新模板
            BeanUtils.copyProperties(dto, template);
            template.setUpdateTime(new Date());
            return updateById(template);
        } catch (Exception e) {
            log.error("更新模板[{}]失败：{}", dto.getId(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Integer id, String companyCode) {
        try {
            log.debug("删除模板，ID：{}", id);
            
            ModbusTemplatePO template = getById(id);
            if (template == null) {
                return false;
            }
            
            // 使用 SecurityUtil 获取公司编码
            String currentCompanyCode = companyCode;
            if (currentCompanyCode == null) {
                currentCompanyCode = SecurityUtil.getCompanySite();
            }
            
            // 校验公司编码
            if (!template.getCompanyCode().equals(currentCompanyCode)) {
                throw new CommonException("无权删除该模板");
            }
            
            // 删除模板
            boolean success = removeById(id);
            
            // 删除关联的寄存器配置
            if (success) {
                QueryWrapper<ModbusRegisterPO> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("template_id", id);
                modbusRegisterMapper.delete(queryWrapper);
            }
            
            return success;
        } catch (Exception e) {
            log.error("删除模板[{}]失败：{}", id, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer copyTemplate(Integer sourceId, String newName, String companyCode) {
        try {
            log.debug("复制模板，源ID：{}，新名称：{}", sourceId, newName);
            
            // 获取源模板
            ModbusTemplatePO sourceTemplate = getById(sourceId);
            if (sourceTemplate == null) {
                throw new CommonException("源模板不存在");
            }
            
            // 使用 SecurityUtil 获取公司编码
            String currentCompanyCode = companyCode;
            if (currentCompanyCode == null) {
                currentCompanyCode = SecurityUtil.getCompanySite();
            }
            
            // 校验公司编码
            if (!sourceTemplate.getCompanyCode().equals(currentCompanyCode)) {
                throw new CommonException("无权复制该模板");
            }
            
            // 检查新模板名称是否已存在
            QueryWrapper<ModbusTemplatePO> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", newName)
                    .eq("company_code", currentCompanyCode)
                    .eq("deleted", 0);
            boolean exists = baseMapper.exists(queryWrapper);
            if (exists) {
                throw new CommonException("模板名称已存在，请更换名称");
            }
            
            // 创建新模板
            ModbusTemplatePO newTemplate = new ModbusTemplatePO();
            BeanUtils.copyProperties(sourceTemplate, newTemplate);
            
            // 重置ID和设置新名称
            newTemplate.setId(null);
            newTemplate.setName(newName);
            newTemplate.setVersion("v1.0");
            
            // 设置时间和删除标记
            Date now = new Date();
            newTemplate.setCreateTime(now);
            newTemplate.setUpdateTime(now);
            newTemplate.setDeleted(0);
            
            // 保存新模板
            save(newTemplate);
            
            // 复制寄存器配置
            QueryWrapper<ModbusRegisterPO> registerQueryWrapper = new QueryWrapper<>();
            registerQueryWrapper.eq("template_id", sourceId);
            List<ModbusRegisterPO> registers = modbusRegisterMapper.selectList(registerQueryWrapper);
            
            for (ModbusRegisterPO register : registers) {
                ModbusRegisterPO newRegister = new ModbusRegisterPO();
                BeanUtils.copyProperties(register, newRegister);
                
                // 重置ID和设置模板ID
                newRegister.setId(null);
                newRegister.setTemplateId(newTemplate.getId());
                
                // 设置时间和删除标记
                newRegister.setCreateTime(now);
                newRegister.setUpdateTime(now);
                newRegister.setDeleted(0);
                
                modbusRegisterMapper.insert(newRegister);
            }
            
            return newTemplate.getId();
        } catch (Exception e) {
            log.error("复制模板失败：{}", e.getMessage(), e);
            return null;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createTemplateWithRegisters(ModbusTemplateWithRegistersDTO dto) {
        // 设置公司编码
        if (dto.getCompanyCode() == null) {
            dto.setCompanyCode(SecurityUtil.getCompanySite());
        }
        
        // 检查模板名称是否已存在
        QueryWrapper<ModbusTemplatePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", dto.getName())
                .eq("company_code", dto.getCompanyCode())
                .eq("deleted", 0);
        boolean exists = baseMapper.exists(queryWrapper);
        if (exists) {
            throw new CommonException("模板名称已存在，请更换名称");
        }
        
        // 将 dto 拷贝到新对象中，而不是直接修改原 dto
        ModbusTemplateWithRegistersDTO newDto = new ModbusTemplateWithRegistersDTO();
        BeanUtils.copyProperties(dto, newDto);
        
        // 设置时间和删除标记
        Date now = new Date();
        newDto.setCreateTime(now);
        newDto.setUpdateTime(now);
        newDto.setDeleted(0);
        
        // 1. 创建模板
        ModbusTemplatePO template = new ModbusTemplatePO();
        BeanUtils.copyProperties(newDto, template);
        
        // 保存模板
        save(template);
        Integer templateId = template.getId();
        
        // 2. 批量保存寄存器
        List<ModbusRegisterDTO> registerDTOs = newDto.getRegisters();
        for (ModbusRegisterDTO registerDTO : registerDTOs) {
            // 复制DTO到新对象
            ModbusRegisterDTO newRegisterDto = new ModbusRegisterDTO();
            BeanUtils.copyProperties(registerDTO, newRegisterDto);
            
            // 设置模板ID和时间信息
            newRegisterDto.setTemplateId(templateId);

            
            // 创建寄存器实体并保存
            ModbusRegisterPO register = new ModbusRegisterPO();
            BeanUtils.copyProperties(newRegisterDto, register);
            modbusRegisterMapper.insert(register);
        }
        
        return templateId;
    }
}