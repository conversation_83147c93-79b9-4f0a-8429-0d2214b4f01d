package cn.jihong.equipment.api.model.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Modbus寄存器数据传输对象
 */
@Data
public class ModbusRegisterDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 寄存器ID（新增时为null）
     */
    private Integer id;

    /**
     * 模板ID
     */
    private Integer templateId;

    /**
     * 寄存器地址
     */
    @NotNull(message = "寄存器地址不能为空")
    @Min(value = 0, message = "寄存器地址必须大于等于0")
    private Integer address;

    /**
     * 数据类型，如float,int
     */
    @NotBlank(message = "数据类型不能为空")
    private String dataType;

    /**
     * 说明
     */
    private String description;

    /**
     * 字段字典ID
     */
    private Integer fieldId;

    /**
     * 字段名称（非数据库字段，用于显示）
     */
    private String fieldName;

    /**
     * 字段值（非数据库字段，用于显示）
     */
    private String fieldValue;

    /**
     * 单位，如V、A、℃
     */
    private String unit;

    /**
     * 缩放因子
     */
    private BigDecimal scalingFactor = BigDecimal.ONE;

    /**
     * 寄存器类型
     * 0-线圈状态(COIL)
     * 1-离散输入状态(DISCRETE_INPUT)
     * 3-输入寄存器(INPUT)
     * 4-保持寄存器(HOLDING)
     */
    @NotNull(message = "寄存器类型不能为空")
    private Integer registerType;


} 