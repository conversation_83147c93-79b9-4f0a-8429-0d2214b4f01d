package cn.jihong.equipment.api.service;

import cn.jihong.equipment.api.model.po.DeviceFieldDataRecordPO;
import cn.jihong.equipment.api.model.vo.DeviceFieldHistoryInVO;
import cn.jihong.equipment.api.model.vo.DeviceFieldHistoryTableVO;
import cn.jihong.equipment.api.model.vo.out.DeviceFieldDataBatchVO;
import cn.jihong.equipment.api.model.vo.out.DeviceFieldMappingVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备数据服务接口
 * 整合设备数据查询和字段映射的业务逻辑
 * 支持新的字段数据记录查询和原有的历史数据查询
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface IDeviceDataService {

    /**
     * 获取设备字段映射关系
     *
     * @param deviceId 设备ID
     * @return 字段映射列表
     */
    List<DeviceFieldMappingVO> getDeviceFieldMapping(Long deviceId);

    /**
     * 获取设备启用的字段映射关系
     *
     * @param deviceId 设备ID
     * @return 启用的字段映射列表
     */
    List<DeviceFieldMappingVO> getEnabledDeviceFieldMapping(Long deviceId);

    // ========== 新的字段数据记录查询方法 ==========

    /**
     * 查询设备字段数据记录（按批次分组）
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @return 按批次分组的字段数据记录列表
     */
    List<DeviceFieldDataBatchVO> getDeviceFieldDataRecordsByBatch(Integer deviceId, String startTime, Date endTime);

    /**
     * 查询设备字段数据记录
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> getDeviceFieldDataRecords(Integer deviceId, String startTime, Date endTime);

    /**
     * 获取设备最新的字段数据记录
     *
     * @param deviceId 设备ID
     * @return 最新的字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> getLatestDeviceFieldData(Integer deviceId);

    /**
     * 根据批次ID查询字段数据记录
     *
     * @param batchId 批次ID
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> getFieldDataByBatchId(String batchId);

    /**
     * 根据公司编码查询字段数据记录
     *
     * @param companyCode 公司编码
     * @param limit       限制条数
     * @return 字段数据记录列表
     */
    List<DeviceFieldDataRecordPO> getCompanyFieldDataRecords(String companyCode, Integer limit);

    /**
     * 获取设备字段数据统计信息
     *
     * @param deviceId  设备ID
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @return 统计信息
     */
    Map<String, Object> getDeviceFieldDataStats(Integer deviceId, String startTime, Date endTime);

    /**
     * 将字段数据记录转换为Map格式（兼容原有接口）
     *
     * @param records 字段数据记录列表
     * @return 转换后的Map数据
     */
    Map<String, Object> convertFieldRecordsToMap(List<DeviceFieldDataRecordPO> records);

    /**
     * 根据设备ID和字段编码获取最新的字段值
     *
     * @param deviceId  设备ID
     * @param fieldCode 字段编码
     * @return 最新的字段数据记录
     */
    DeviceFieldDataRecordPO getLatestFieldValue(Integer deviceId, String fieldCode);

    /**
     * 批量查询设备字段历史数据（表格结构）
     * @param inVO 查询参数
     * @return 表格结构
     */
    DeviceFieldHistoryTableVO getDeviceFieldHistoryTable(DeviceFieldHistoryInVO inVO);

} 