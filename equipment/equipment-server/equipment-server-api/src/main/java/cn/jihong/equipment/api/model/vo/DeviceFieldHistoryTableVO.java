package cn.jihong.equipment.api.model.vo;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 设备字段历史查询返回表格结构
 */
@Data
public class DeviceFieldHistoryTableVO {
    /**
     * 表头（字段名列表）
     */
    private List<String> headers;

    /**
     * 数据体（每行为一个采集记录，key为字段名或编码）
     */
    private List<Map<String, Object>> records;

    public void setRecords(List<Map<String, Object>> records) {
        this.records = records;
    }
} 