package cn.jihong.equipment.api.model.dto;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Modbus模板及寄存器数据传输对象
 *
 * <AUTHOR>
 * @since 2024-05-12
 */
@Data
public class ModbusTemplateWithRegistersDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 模板ID（新增时为null）
     */
    private Integer id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String name;

    /**
     * 所属公司编码
     */
    private String companyCode;

    /**
     * 模板版本
     */
    private String version;

    /**
     * 模板描述
     */
    private String description;

    /**
     * 寄存器列表
     */
    @NotEmpty(message = "寄存器列表不能为空")
    @Valid
    private List<ModbusRegisterDTO> registers;
    
    /**
     * 创建人
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Integer deleted;
} 