package cn.jihong.equipment.api.model.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import java.util.List;

/**
 * 设备字段历史查询请求参数
 */
@Data
public class DeviceFieldHistoryInVO {
    /**
     * 设备ID，可为空
     */
    private Integer deviceId;

    /**
     * 字段编码列表
     */
    private List<String> fieldCodes;

    /**
     * 查询起始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startTime;

    /**
     * 查询结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
} 